#!/usr/bin/env python3
"""
Test script to verify RSA key creation returns complete CTM response
"""

import requests
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_rsa_key_creation():
    """Test RSA key creation and verify CTM response includes RSA-specific fields"""
    
    # API endpoint
    base_url = "http://localhost:3001"
    endpoint = f"{base_url}/api/v1/ctm/keys/upload"
    
    # Test data for RSA key
    test_data = {
        "key_name": f"test_rsa_key_ctm_response_{int(os.urandom(4).hex(), 16)}",
        "algorithm": "RSA",
        "num_bytes": 2048,  # 2048 bits
        "owner": "test_user",
        "exportable": False
    }
    
    print("🔥 Testing RSA key creation with CTM response...")
    print(f"📝 Test data: {json.dumps(test_data, indent=2)}")
    
    try:
        # Make the request
        response = requests.post(endpoint, json=test_data, headers={'Content-Type': 'application/json'})
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Request successful!")
            print(f"📋 Response structure:")
            
            # Check basic fields
            data = result.get('data', {})
            print(f"  - Key name: {data.get('key_name')}")
            print(f"  - Algorithm: {data.get('algorithm')}")
            print(f"  - CTM Key ID: {data.get('ctm_key_id')}")
            print(f"  - Upload successful: {data.get('upload_successful')}")
            
            # Check RSA-specific fields from SeQRNG
            print(f"\n🔐 SeQRNG RSA fields:")
            print(f"  - RSA public key present: {'rsa_public_key' in data}")
            print(f"  - Key size: {data.get('key_size')}")
            
            if 'rsa_public_key' in data:
                rsa_pub_key = data['rsa_public_key']
                print(f"  - RSA public key preview: {rsa_pub_key[:50]}...")
            
            # Check CTM-specific RSA fields
            print(f"\n🏛️ CTM RSA fields:")
            print(f"  - CTM public key present: {'ctm_public_key' in data}")
            print(f"  - CTM links present: {'ctm_links' in data}")
            print(f"  - CTM key size: {data.get('ctm_key_size')}")
            print(f"  - CTM object type: {data.get('ctm_object_type')}")
            print(f"  - CTM full response present: {'ctm_full_response' in data}")
            
            if 'ctm_public_key' in data:
                ctm_pub_key = data['ctm_public_key']
                print(f"  - CTM public key preview: {ctm_pub_key[:50]}...")
            
            if 'ctm_links' in data:
                links = data['ctm_links']
                print(f"  - CTM links count: {len(links)}")
                for i, link in enumerate(links):
                    print(f"    Link {i+1}: type={link.get('type')}, targetID={link.get('targetID')}")
            
            if 'ctm_full_response' in data:
                ctm_response = data['ctm_full_response']
                print(f"  - CTM response keys: {list(ctm_response.keys())}")
                
                # Check for specific CTM fields mentioned in the documentation
                ctm_fields_to_check = ['id', 'uri', 'account', 'application', 'publickey', 'objectType', 'links', 'size']
                for field in ctm_fields_to_check:
                    if field in ctm_response:
                        print(f"    ✅ {field}: present")
                    else:
                        print(f"    ❌ {field}: missing")
            
            # Verify the key differences between SeQRNG and CTM public keys
            if 'rsa_public_key' in data and 'ctm_public_key' in data:
                seqrng_key = data['rsa_public_key'].strip()
                ctm_key = data['ctm_public_key'].strip()
                
                print(f"\n🔍 Key comparison:")
                print(f"  - SeQRNG and CTM keys are identical: {seqrng_key == ctm_key}")
                if seqrng_key != ctm_key:
                    print(f"  - SeQRNG key length: {len(seqrng_key)}")
                    print(f"  - CTM key length: {len(ctm_key)}")
            
            return True
            
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        return False

def test_aes_key_creation():
    """Test AES key creation to ensure non-RSA keys still work"""
    
    # API endpoint
    base_url = "http://localhost:3001"
    endpoint = f"{base_url}/api/v1/ctm/keys/upload"
    
    # Test data for AES key
    test_data = {
        "key_name": f"test_aes_key_comparison_{int(os.urandom(4).hex(), 16)}",
        "algorithm": "AES",
        "num_bytes": 32,  # 256 bits
        "owner": "test_user",
        "exportable": False
    }
    
    print("\n🔥 Testing AES key creation for comparison...")
    print(f"📝 Test data: {json.dumps(test_data, indent=2)}")
    
    try:
        # Make the request
        response = requests.post(endpoint, json=test_data, headers={'Content-Type': 'application/json'})
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ AES key creation successful!")
            
            data = result.get('data', {})
            print(f"📋 AES Response structure:")
            print(f"  - Key name: {data.get('key_name')}")
            print(f"  - Algorithm: {data.get('algorithm')}")
            print(f"  - CTM Key ID: {data.get('ctm_key_id')}")
            
            # Verify RSA-specific fields are NOT present for AES keys
            rsa_fields = ['rsa_public_key', 'ctm_public_key', 'ctm_links', 'ctm_object_type']
            print(f"\n🔍 RSA fields should NOT be present for AES:")
            for field in rsa_fields:
                present = field in data
                print(f"  - {field}: {'❌ Present (unexpected)' if present else '✅ Not present (expected)'}")
            
            return True
            
        else:
            print(f"❌ AES request failed with status {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error during AES test: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 Testing RSA CTM Response Enhancement")
    print("=" * 50)
    
    # Test RSA key creation
    rsa_success = test_rsa_key_creation()
    
    # Test AES key creation for comparison
    aes_success = test_aes_key_creation()
    
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    print(f"  - RSA key test: {'✅ PASSED' if rsa_success else '❌ FAILED'}")
    print(f"  - AES key test: {'✅ PASSED' if aes_success else '❌ FAILED'}")
    
    if rsa_success and aes_success:
        print("\n🎉 All tests passed! RSA CTM response enhancement is working correctly.")
    else:
        print("\n⚠️ Some tests failed. Please check the implementation.")
