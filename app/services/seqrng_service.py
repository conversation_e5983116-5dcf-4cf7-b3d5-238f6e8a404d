"""
SeQRNG Service Module

This module provides a service class for interacting with the SeQRNG (Sequre Quantum Random Number Generator)
API to generate quantum random numbers and keys. It supports various output formats including raw bytes,
hexadecimal, alphanumeric, and RSA key pairs.

The service handles:
- Quantum random byte generation
- Hexadecimal key generation
- Alphanumeric key generation
- RSA key pair generation using quantum entropy
- Key material generation for CTM upload

All generated keys include entropy reports with quantum fidelity information.
"""

import base64
import os
import subprocess
import requests
import tempfile
from typing import Dict, Any, Tu<PERSON>
from interface_seqrng_v2 import sq_get_random_bytes, get_random_hex_key, get_random_alphanumeric_key


class SeQRNGService:
    """
    Service class for SeQRNG (Sequre Quantum Random Number Generator) operations.

    This service provides methods to interact with the SeQRNG API for generating
    quantum random numbers in various formats. It handles authentication,
    request formatting, and response processing.

    Attributes:
        base_url (str): The base URL of the SeQRNG API server
        api_token (str): Authentication token for SeQRNG API access
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize SeQRNG service with configuration.

        Args:
            config (Dict[str, Any]): SeQRNG configuration dictionary containing:
                - base_url (str): SeQRNG server URL (e.g., "http://server:1982")
                - api_token (str): API authentication token

        Raises:
            KeyError: If required configuration keys are missing
        """
        self.base_url = config['base_url']
        self.api_token = config['api_token']
    
    def generate_random_bytes(self, num_bytes: int, packages: int = 1) -> Dict[str, Any]:
        """
        Generate random bytes from SeQRNG
        
        Args:
            num_bytes: Number of bytes to generate
            packages: Number of packages to generate
            
        Returns:
            Dictionary containing generated bytes and metadata
        """
        # Generate random bytes
        random_bytes, error_str, entropy_str, entropy_status = sq_get_random_bytes(
            num_bytes, packages, self.base_url, self.api_token
        )
        
        # Convert bytes to base64 for JSON response
        random_bytes_b64 = base64.b64encode(random_bytes).decode('utf-8')
        
        return {
            "random_bytes_base64": random_bytes_b64,
            "random_bytes_raw": random_bytes,  # For internal use
            "num_bytes": num_bytes,
            "packages": packages,
            "entropy_report": {
                "quantum_fidelity": error_str,
                "entropy_string": entropy_str,
                "entropy_status": entropy_status
            }
        }
    
    def generate_hex_key(self, num_bytes: int) -> Dict[str, Any]:
        """
        Generate random hexadecimal key from SeQRNG
        
        Args:
            num_bytes: Number of bytes to generate
            
        Returns:
            Dictionary containing generated hex key and metadata
        """
        # Generate hex key
        hex_key, error_str, entropy_str, entropy_status = get_random_hex_key(
            num_bytes, self.base_url, self.api_token
        )
        
        return {
            "hex_key": hex_key,
            "num_bytes": num_bytes,
            "entropy_report": {
                "quantum_fidelity": error_str,
                "entropy_string": entropy_str,
                "entropy_status": entropy_status
            }
        }
    
    def generate_alphanumeric_key(self, num_bytes: int) -> Dict[str, Any]:
        """
        Generate random alphanumeric key from SeQRNG
        
        Args:
            num_bytes: Number of bytes to generate
            
        Returns:
            Dictionary containing generated alphanumeric key and metadata
        """
        # Generate alphanumeric key
        alphanumeric_key, error_str, entropy_str, entropy_status = get_random_alphanumeric_key(
            num_bytes, self.base_url, self.api_token
        )
        
        return {
            "alphanumeric_key": alphanumeric_key,
            "num_bytes": num_bytes,
            "entropy_report": {
                "quantum_fidelity": error_str,
                "entropy_string": entropy_str,
                "entropy_status": entropy_status
            }
        }
    
    def generate_key_material(self, num_bytes: int) -> Tuple[bytes, Dict[str, Any]]:
        """
        Generate raw key material for CTM upload
        
        Args:
            num_bytes: Number of bytes to generate
            
        Returns:
            Tuple of (raw_bytes, entropy_report)
        """
        print(f"🔥 Generating {num_bytes} quantum random bytes from SeQRNG...")
        key_material, error_str, entropy_str, entropy_status = sq_get_random_bytes(
            num_bytes, 1, self.base_url, self.api_token
        )
        print(f"✅ Generated key material: {len(key_material)} bytes")
        print(f"🔍 Key material (hex): {key_material.hex()[:32]}...")  # Show first 16 bytes
        
        entropy_report = {
            "source": "seqrng",
            "quantum_fidelity": error_str,
            "entropy_string": entropy_str,
            "entropy_status": entropy_status
        }
        
        return key_material, entropy_report

    def generate_rsa_key_pair(self, key_size: int = 2048) -> Dict[str, Any]:
        """
        Generate RSA key pair using SeQRNG entropy

        Args:
            key_size: RSA key size in bits (default: 2048)

        Returns:
            Dictionary containing RSA private key, public key, and entropy report
        """
        print(f"🔥 Generating RSA {key_size}-bit key pair using SeQRNG entropy...")

        # Check if OpenSSL is available
        try:
            version_result = subprocess.run(["openssl", "version"], capture_output=True, text=True, check=True)
            print(f"🔍 Using OpenSSL: {version_result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError) as e:
            error_msg = "OpenSSL is not available in the system"
            print(f"❌ {error_msg}")
            raise Exception(error_msg)

        # Create temporary directory for files
        with tempfile.TemporaryDirectory() as temp_dir:
            random_file = os.path.join(temp_dir, "random_data.bin")
            private_key_file = os.path.join(temp_dir, "private_key.pem")
            public_key_file = os.path.join(temp_dir, "public_key.pem")

            try:
                # Step 1: Generate random entropy file from SeQRNG
                entropy_size = 256  # 256 bytes of entropy
                print(f"🎲 Generating {entropy_size} bytes of quantum entropy...")

                # Try SeQRNG first
                try:
                    random_bytes, error_str, entropy_str, entropy_status = sq_get_random_bytes(
                        entropy_size, 1, self.base_url, self.api_token
                    )

                    if len(random_bytes) >= entropy_size:
                        print("✅ QRNG call was successful")
                        with open(random_file, "wb") as f:
                            f.write(random_bytes)
                        entropy_source = "seqrng"
                    else:
                        raise Exception("Insufficient entropy from SeQRNG")

                except Exception as e:
                    print(f"⚠️ QRNG offline, using PRNG data: {str(e)}")
                    with open(random_file, "wb") as f:
                        f.write(os.urandom(entropy_size))
                    error_str = f"SeQRNG fallback: {str(e)}"
                    entropy_str = "PRNG fallback"
                    entropy_status = "fallback"
                    entropy_source = "prng_fallback"

                print(f"✅ Random entropy file created with {entropy_size} bytes")

                # Step 2: Generate RSA Private Key using OpenSSL
                print(f"🔐 Generating RSA private key ({key_size} bits)...")

                # Try modern OpenSSL command first, fallback to older syntax
                try:
                    subprocess.run([
                        "openssl", "genpkey",
                        "-algorithm", "RSA",
                        "-out", private_key_file,
                        "-pkeyopt", f"rsa_keygen_bits:{key_size}",
                        "-rand", random_file
                    ], check=True, capture_output=True, text=True)
                except subprocess.CalledProcessError:
                    # Fallback to older OpenSSL syntax
                    print("🔄 Trying alternative OpenSSL command...")
                    subprocess.run([
                        "openssl", "genrsa",
                        "-out", private_key_file,
                        "-rand", random_file,
                        str(key_size)
                    ], check=True, capture_output=True, text=True)

                print(f"✅ RSA Private Key generated")

                # Step 3: Extract Public Key from Private Key
                print("🔓 Extracting RSA public key...")
                subprocess.run([
                    "openssl", "rsa",
                    "-in", private_key_file,
                    "-pubout",
                    "-out", public_key_file
                ], check=True, capture_output=True, text=True)

                print(f"✅ RSA Public Key extracted")

                # Read the generated keys
                with open(private_key_file, 'r') as f:
                    private_key_pem = f.read()

                with open(public_key_file, 'r') as f:
                    public_key_pem = f.read()

                # Convert private key to PKCS#1 DER format for CTM upload
                # CTM expects: PEM-encoded ASN.1 DER-encoded PKCS #1 format
                private_key_pkcs1_file = os.path.join(temp_dir, "private_key_pkcs1.der")
                subprocess.run([
                    "openssl", "rsa",
                    "-in", private_key_file,
                    "-outform", "DER",
                    "-out", private_key_pkcs1_file
                ], check=True, capture_output=True, text=True)

                # Read the PKCS#1 DER format private key
                with open(private_key_pkcs1_file, 'rb') as f:
                    private_key_der = f.read()

                print(f"✅ Private key converted to PKCS#1 DER format: {len(private_key_der)} bytes")

                # Prepare entropy report
                entropy_report = {
                    "source": entropy_source,
                    "quantum_fidelity": error_str,
                    "entropy_string": entropy_str,
                    "entropy_status": entropy_status,
                    "entropy_bytes": entropy_size
                }

                print("🎉 RSA Key Pair successfully created!")

                return {
                    "private_key": private_key_pem,
                    "private_key_der": private_key_der,
                    "public_key": public_key_pem,
                    "key_size": key_size,
                    "algorithm": "RSA",
                    "entropy_report": entropy_report
                }

            except subprocess.CalledProcessError as e:
                error_msg = f"OpenSSL command failed: {e.stderr if e.stderr else str(e)}"
                print(f"❌ {error_msg}")
                print(f"🔍 Command that failed: {' '.join(e.cmd) if hasattr(e, 'cmd') else 'Unknown'}")
                print(f"🔍 Return code: {e.returncode}")

                # Try to get OpenSSL version for debugging
                try:
                    version_result = subprocess.run(["openssl", "version"], capture_output=True, text=True)
                    print(f"🔍 OpenSSL version: {version_result.stdout.strip()}")
                except:
                    print("🔍 Could not determine OpenSSL version")

                raise Exception(error_msg)
            except Exception as e:
                error_msg = f"RSA key generation failed: {str(e)}"
                print(f"❌ {error_msg}")
                raise Exception(error_msg)
