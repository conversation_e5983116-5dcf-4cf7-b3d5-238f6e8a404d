"""
Response helper utilities
"""

from flask import jsonify
from datetime import datetime
from typing import Any, Dict, <PERSON><PERSON>


def create_error_response(message: str, status_code: int = 400, error_type: str = "error") -> Tuple[Any, int]:
    """
    Create standardized error response
    
    Args:
        message: Error message
        status_code: HTTP status code
        error_type: Type of error
        
    Returns:
        Tuple of (response, status_code)
    """
    return jsonify({
        "status": "error",
        "error_type": error_type,
        "message": message,
        "timestamp": datetime.utcnow().isoformat() + "Z"
    }), status_code


def create_success_response(data: Dict[str, Any], message: str = "Success") -> Any:
    """
    Create standardized success response
    
    Args:
        data: Response data
        message: Success message
        
    Returns:
        JSON response
    """
    return jsonify({
        "status": "success",
        "message": message,
        "data": data,
        "timestamp": datetime.utcnow().isoformat() + "Z"
    })
