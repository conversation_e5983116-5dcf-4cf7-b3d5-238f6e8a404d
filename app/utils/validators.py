"""
Input validation utilities
"""

from flask import request
from typing import <PERSON><PERSON>, <PERSON><PERSON>, Any
from app.utils.response_helpers import create_error_response
from app.config.environment import Environment


def validate_json_request() -> Optional[Tuple[Any, int]]:
    """
    Validate that request contains valid JSON
    
    Returns:
        Error response if validation fails, None if valid
    """
    if not request.is_json:
        return create_error_response("Request must contain valid JSON", 400, "validation_error")
    return None


def validate_key_name(key_name: str) -> Optional[str]:
    """
    Validate key name format
    
    Args:
        key_name: Key name to validate
        
    Returns:
        Error message if invalid, None if valid
    """
    if not key_name or not isinstance(key_name, str):
        return "Key name must be a non-empty string"
    if len(key_name) > Environment.MAX_KEY_NAME_LENGTH:
        return f"Key name must be {Environment.MAX_KEY_NAME_LENGTH} characters or less"
    if not key_name.replace('_', '').replace('-', '').isalnum():
        return "Key name can only contain alphanumeric characters, underscores, and hyphens"
    return None


def validate_algorithm_key_length(algorithm: str, num_bytes: int) -> bool:
    """
    Validate if algorithm supports the specified key length
    
    Args:
        algorithm: Algorithm name
        num_bytes: Key length in bytes
        
    Returns:
        True if valid, False otherwise
    """
    algorithm_lower = algorithm.lower()
    
    if algorithm_lower == 'aes' and num_bytes not in (16, 24, 32):
        return False
    elif algorithm_lower == 'rsa':
        # For RSA, num_bytes can be either bits or bytes
        # Common RSA sizes in bits: 1024, 2048, 3072, 4096
        # Common RSA sizes in bytes: 128, 256, 384, 512
        valid_bits = (1024, 2048, 3072, 4096)
        valid_bytes = (128, 256, 384, 512)
        if num_bytes not in valid_bits and num_bytes not in valid_bytes:
            return False
    elif algorithm_lower == 'hmac':
        # HMAC can use various key lengths, but common ones are:
        if num_bytes not in (16, 24, 32, 64):
            return False
    
    return True


def validate_num_bytes(num_bytes: Any) -> Optional[str]:
    """
    Validate num_bytes parameter
    
    Args:
        num_bytes: Number of bytes to validate
        
    Returns:
        Error message if invalid, None if valid
    """
    if not isinstance(num_bytes, int) or num_bytes < Environment.MIN_BYTES or num_bytes > Environment.MAX_BYTES:
        return f"num_bytes must be an integer between {Environment.MIN_BYTES} and {Environment.MAX_BYTES}"
    return None


def validate_packages(packages: Any) -> Optional[str]:
    """
    Validate packages parameter
    
    Args:
        packages: Number of packages to validate
        
    Returns:
        Error message if invalid, None if valid
    """
    if not isinstance(packages, int) or packages < Environment.MIN_PACKAGES or packages > Environment.MAX_PACKAGES:
        return f"packages must be an integer between {Environment.MIN_PACKAGES} and {Environment.MAX_PACKAGES}"
    return None


def validate_key_count(key_count: Any) -> Optional[str]:
    """
    Validate key_count parameter
    
    Args:
        key_count: Number of keys to validate
        
    Returns:
        Error message if invalid, None if valid
    """
    if not isinstance(key_count, int) or key_count < Environment.MIN_KEY_COUNT or key_count > Environment.MAX_KEY_COUNT:
        return f"key_count must be an integer between {Environment.MIN_KEY_COUNT} and {Environment.MAX_KEY_COUNT}"
    return None
