"""
Health and Documentation Controller Module

This module provides endpoints for API health monitoring, configuration status,
and interactive documentation. It includes:

- Health check endpoint for monitoring API status
- Configuration status endpoint for checking loaded configurations
- API documentation endpoint with comprehensive endpoint information
- Swagger UI interface for interactive API exploration
- OpenAPI specification serving

The health endpoints are essential for monitoring and debugging the API,
while the documentation endpoints provide user-friendly access to API specifications.
"""

from flask import Blueprint, request, render_template_string, current_app
from app.utils.response_helpers import create_success_response, create_error_response
from app.config.environment import Environment
import os

health_bp = Blueprint('health', __name__)

# Swagger UI HTML template
SWAGGER_UI_HTML = """
<!DOCTYPE html>
<html>
<head>
    <title>SeQRNG-CTM API Documentation</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui.css" />
    <style>
        html {
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }
        *, *:before, *:after {
            box-sizing: inherit;
        }
        body {
            margin:0;
            background: #fafafa;
        }
    </style>
</head>
<body>
    <div id="swagger-ui"></div>
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            const ui = SwaggerUIBundle({
                url: '/swagger.yaml',
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout"
            });
        };
    </script>
</body>
</html>
"""


@health_bp.route('/api/v1/health', methods=['GET'])
def health_check():
    """
    Health check endpoint for API monitoring.

    Provides comprehensive health status including:
    - Service information (name, version)
    - Configuration loading status
    - Available endpoints summary

    Returns:
        JSON response with health status and service information

    Raises:
        500: If health check fails due to internal error
    """
    try:
        # Check if configuration is loaded
        seqrng_config = current_app.config.get('SEQRNG_CONFIG')
        ctm_config = current_app.config.get('CTM_CONFIG')
        config_status = seqrng_config is not None and ctm_config is not None

        return create_success_response({
            "service": Environment.API_TITLE,
            "version": Environment.API_VERSION,
            "status": "healthy",
            "configuration_loaded": config_status,
            "endpoints": {
                "key_generation": "/api/v1/keys/generate/*",
                "ctm_management": "/api/v1/ctm/*",
                "health": "/api/v1/health",
                "config": "/api/v1/config/status",
                "docs": "/api/v1/docs"
            }
        })
    except Exception as e:
        return create_error_response(f"Health check failed: {str(e)}", 500)


@health_bp.route('/api/v1/docs', methods=['GET'])
def api_documentation():
    """API documentation endpoint"""
    docs = {
        "title": Environment.API_TITLE,
        "version": Environment.API_VERSION,
        "description": Environment.API_DESCRIPTION,
        "base_url": request.base_url.replace('/api/v1/docs', ''),
        "endpoints": {
            "health_check": {
                "method": "GET",
                "path": "/api/v1/health",
                "description": "Check API health status"
            },
            "config_status": {
                "method": "GET",
                "path": "/api/v1/config/status",
                "description": "Get configuration status"
            },
            "generate_bytes": {
                "method": "POST",
                "path": "/api/v1/keys/generate/bytes",
                "description": "Generate random bytes from SeQRNG",
                "parameters": {
                    "num_bytes": f"integer ({Environment.MIN_BYTES}-{Environment.MAX_BYTES}, default: 32)",
                    "packages": f"integer ({Environment.MIN_PACKAGES}-{Environment.MAX_PACKAGES}, default: 1)"
                }
            },
            "generate_hex": {
                "method": "POST",
                "path": "/api/v1/keys/generate/hex",
                "description": "Generate hexadecimal key from SeQRNG",
                "parameters": {
                    "num_bytes": f"integer ({Environment.MIN_BYTES}-{Environment.MAX_BYTES}, default: 32)"
                }
            },
            "generate_alphanumeric": {
                "method": "POST",
                "path": "/api/v1/keys/generate/alphanumeric",
                "description": "Generate alphanumeric key from SeQRNG",
                "parameters": {
                    "num_bytes": f"integer ({Environment.MIN_BYTES}-{Environment.MAX_BYTES}, default: 32)"
                }
            },
            "ctm_get_token": {
                "method": "GET",
                "path": "/api/v1/ctm/auth/token",
                "description": "Get CTM authentication token"
            },
            "ctm_check_key": {
                "method": "GET",
                "path": "/api/v1/ctm/keys/<key_name>/exists",
                "description": "Check if a key exists in CTM"
            },
            "ctm_upload_key": {
                "method": "POST",
                "path": "/api/v1/ctm/keys/upload",
                "description": "Upload a single key to CTM",
                "parameters": {
                    "key_name": "string (required)",
                    "algorithm": "string (required, e.g., 'AES', 'RSA', 'HMAC')",
                    "num_bytes": f"integer ({Environment.MIN_BYTES}-{Environment.MAX_BYTES}, required)",
                    "owner": "string (required)",
                    "exportable": "boolean (default: false)",
                    "key_material_base64": "string (optional, if not provided, will be generated)"
                }
            },
            "ctm_upload_batch": {
                "method": "POST",
                "path": "/api/v1/ctm/keys/upload/batch",
                "description": "Upload multiple keys to CTM",
                "parameters": {
                    "key_base_name": "string (required)",
                    "algorithm": "string (required)",
                    "num_bytes": f"integer ({Environment.MIN_BYTES}-{Environment.MAX_BYTES}, required)",
                    "key_count": f"integer ({Environment.MIN_KEY_COUNT}-{Environment.MAX_KEY_COUNT}, required)",
                    "owner": "string (required)",
                    "exportable": "boolean (default: false)"
                }
            }
        },
        "swagger_ui": "/docs",
        "openapi_spec": "/swagger.yaml"
    }

    return create_success_response(docs, "API documentation retrieved successfully")


@health_bp.route('/api/v1/config/status', methods=['GET'])
def config_status():
    """Get configuration status"""
    try:
        seqrng_config = current_app.config.get('SEQRNG_CONFIG')
        ctm_config = current_app.config.get('CTM_CONFIG')
        config_loaded = seqrng_config is not None and ctm_config is not None
        
        status_data = {
            "configuration_loaded": config_loaded
        }
        
        if config_loaded:
            status_data.update({
                "seqrng_url": seqrng_config['base_url'],
                "ctm_url": ctm_config['base_url'],
                "ctm_domain": ctm_config['domain']
            })
        
        return create_success_response(status_data)
    except Exception as e:
        return create_error_response(f"Failed to get configuration status: {str(e)}", 500)


@health_bp.route('/docs')
def swagger_ui():
    """Serve Swagger UI"""
    return render_template_string(SWAGGER_UI_HTML)


@health_bp.route('/swagger.yaml')
def swagger_spec():
    """Serve the swagger.yaml file"""
    try:
        swagger_path = os.path.join(current_app.root_path, 'static', 'swagger.yaml')
        with open(swagger_path, 'r') as f:
            swagger_content = f.read()
        return swagger_content, 200, {'Content-Type': 'application/x-yaml'}
    except FileNotFoundError:
        return create_error_response("Swagger specification not found", 404, "not_found")
