from urllib3.exceptions import InsecureRequestWarning
import requests
import random
import base64
import time
import os
import sys
import json

def get_valid_input(valid_options, prompt_text=None, default_value=None):
    """
    Prompt the user for input until they enter one of the valid options.
    Allows a default value when user presses Enter.

    Parameters:
        valid_options (list of str): The allowed input values.
        prompt_text (str): Optional custom message to show to the user.
        default_value (str): Optional default to use if the user presses Enter.

    Returns:
        str: The valid input entered by the user or the default.
    """
    if not valid_options:
        raise ValueError("List of valid options cannot be empty.")

    valid_options = [option.lower() for option in valid_options]  # Normalize

    #if default_value is not None:
        #default_value = default_value.lower()
        #if default_value not in valid_options:
            #raise ValueError(f"Default value '{default_value}' is not in valid options.")

    # Build prompt
    options_display = ', '.join(valid_options)
    if prompt_text is None:
        prompt_message = f"Choose one ({options_display})"
    else:
        prompt_message = f"{prompt_text} ({options_display})"

    if default_value is not None:
        prompt_message += f" [default: {default_value}]"

    prompt_message += ": "

    # Input loop
    while True:
        user_input_raw = input(prompt_message).strip().lower()
        user_input = user_input_raw.strip().lower()
        if not user_input and default_value is not None:
            return default_value
        elif user_input in valid_options:
            return user_input_raw
        else:
            print(f"Invalid input. Please choose from: {options_display}")


def get_valid_input2(valid_options, prompt_text=None):
    """
    Prompt the user for input until they enter one of the valid options.
    
    Parameters:
        valid_options (list of str): The allowed input values.
        prompt_message (str): Optional custom message to show to the user.
    
    Returns:
        str: The valid input entered by the user.
    """
    if not valid_options:
        raise ValueError("List of valid options cannot be empty.")
    
    valid_options = [option.lower() for option in valid_options]  # Normalize

    if prompt_text is None:
        prompt_message = f"Choose one ({', '.join(valid_options)}): "
    else:
        prompt_message = f"{prompt_text} ({', '.join(valid_options)}): "
    while True:
        user_input = input(prompt_message).strip().lower()
        if user_input in valid_options:
            return user_input
        else:
            print(f"Invalid input. Please choose from: {', '.join(valid_options)}")

    # input_validator.py

def get_integer_input(min_val, max_val, prompt_text=None):
    """
    Prompt the user for an integer input between min_val and max_val (inclusive).
    
    Args:
        min_val (int): Minimum acceptable value.
        max_val (int): Maximum acceptable value.
        prompt (str, optional): Custom prompt text. Default is auto-generated.

    Returns:
        int: Validated integer input from user.
    """
    if prompt_text is None:
        prompt = f"Enter a number between {min_val} and {max_val}: "
    else:
        prompt = f"{prompt_text} (between {min_val} and {max_val}): "    
    while True:
        try:
            user_input = int(input(prompt))
            if min_val <= user_input <= max_val:
                return user_input
            else:
                print(f"Please enter a number between {min_val} and {max_val}.")
        except ValueError:
            print("Invalid input. Please enter a valid integer.")

def str_to_boolean(s):
    """
    Convert a string to a boolean value.
    Accepted true values: 'true', '1', 'yes', 'on'
    Accepted false values: 'false', '0', 'no', 'off'
    """
    s = s.strip().lower()
    if s in ['true', '1', 'yes', 'on']:
        return True
    elif s in ['false', '0', 'no', 'off']:
        return False
    else:
        raise ValueError(f"Cannot convert '{s}' to boolean.")
    


    #----------Begin algorithm and byte check-----------
def check_algorithm_key_bytes(key_material, algorithm):
    key_material_len=len(key_material)
    if algorithm == 'hmac-sha1' and key_material_len not in range(16, 33): # between 16 and 32 bytes in integer values, should update to 128-256 bits
        print("Error: SHA1 key is not proper size. Must be between 128 and 256 bits. Exit program.")
        sys.exit(0)
    elif algorithm == 'hmac-sha256' and key_material_len not in (16,24,32,64):
        print("Error: SHA256 key is not proper size. Must be either (16,24,32,64) bytes. Exit program.")
        sys.exit(0)
    elif algorithm == 'hmac-sha384' and key_material_len not in (24,36,48):
        print("Error: SHA384 key is not proper size. Must be either (24,36,48) bytes. Exit program.")
        sys.exit(0)
    elif algorithm == 'hmac-sha512' and key_material_len not in (32,48,64):
        print("Error: SHA384 key is not proper size. Must be either (32,48,64) bytes. Exit program.")
        sys.exit(0)
    elif algorithm == 'aes' and key_material_len not in (16,24,32):
        print("Error: AES key is not proper size. Must be either (16,24,32) bytes. Exit program.")
        sys.exit(0)
    elif algorithm == 'aria' and key_material_len not in (16,24,32):
        print("Error: Aria key is not proper size. Must be either (16,24,32) bytes. Exit program.")
        sys.exit(0)
    elif algorithm == 'seed' and key_material_len !=16:
        print("Error: SEED key is not proper size. Must be 16 bytes. Exit program.")
        sys.exit(0)
    else:
        print(f"Key for "+algorithm+" algorithm checked and is of proper length:" ,key_material_len) 

# Module to separate N bytes into m groups of n, for multiple keys
def group_bytes(data: bytes, n: int, m: int):
    """
    Splits bytes into m groups of n bytes.
    
    Args:
        data (bytes): The input byte sequence.
        m (int): Number of groups.
        n (int): Size of each group (in bytes).

    Returns:
        List[bytes]: List of m byte groups of length n.

    Raises:
        ValueError: If data length < m * n.
    """
    total_needed = m * n
    if len(data) < total_needed:
        raise ValueError(f"Not enough data: need at least {total_needed} bytes, got {len(data)}.")

    return [data[i*n:(i+1)*n] for i in range(m)]

def check_key_length(key,size): 
# Check key length
    if len(key) == size:
        print("Random seed generated and length is "+str(len(key))+" bytes............success")
        length_status=True
    else:
        print("Error: Key not generated or not equal to required number of bytes. Exit program.")
        length_status=False
    return(length_status) 

# ---------Routine to upload random byte key to  CTM -----------
def ctm_upload_key(base_url, api_key, name_key, key_material, algorithm, own, export, additional_meta=None):
    key_material_len=len(key_material)
    headers = {'Authorization': f'Bearer {api_key}'}

    # Start with basic metadata
    meta_data = {"ownerId": own}

    # Add additional metadata if provided
    if additional_meta:
        meta_data.update(additional_meta)

    # Build CTM key data with algorithm-specific parameters
    if algorithm.upper() == 'RSA':
        # For RSA keys, send PEM format directly (not hex-encoded)
        material_value = key_material.decode('utf-8') if isinstance(key_material, bytes) else key_material
        ctm_key_data = {
            "name": name_key,
            "usageMask": 12,
            "algorithm": algorithm,
            "meta": meta_data,
            "state": "Pre-Active",
            "unexportable": export,
            "material": material_value,
            "objectType": "Private Key",
            "aliases": [ { "alias": name_key, "type": "string" } ]
        }
    else:
        # For other algorithms, use hex encoding
        ctm_key_data = {
            "name": name_key,
            "usageMask": 12,
            "algorithm": algorithm,
            "meta": meta_data,
            "state": "Pre-Active",
            "unexportable": export,
            "material": key_material.hex(),
            "aliases": [ { "alias": name_key, "type": "string" } ]
        }

    # Use base_url directly if it's a complete URL, otherwise treat as IP for backward compatibility
    if base_url.startswith(('http://', 'https://')):
        api_url = f"{base_url.rstrip('/')}/api/v1/vault/keys2"
    else:
        # Backward compatibility: treat as IP address
        api_url = f"https://{base_url}/api/v1/vault/keys2"

    response = requests.post(api_url, headers=headers, json=ctm_key_data, verify=False)
    content = json.loads(response.content)
    ctm_key_id=content['id']
    if (response):
        print(f"Key {name_key} uploaded successfully, CTM Key ID is: ",ctm_key_id)
        return ctm_key_id  # Return the CTM key ID
    else:
        print("Key not uploaded to CipherTrust: Error\nExit program.")
        sys.exit(0)

# ---------Enhanced routine to upload key and return full CTM response -----------
def ctm_upload_key_full_response(base_url, api_key, name_key, key_material, algorithm, own, export, additional_meta=None):
    """
    Upload key to CTM and return the full response including RSA-specific fields

    Args:
        base_url: CTM base URL
        api_key: CTM API key
        name_key: Key name
        key_material: Key material bytes
        algorithm: Key algorithm
        own: Key owner
        export: Whether key is unexportable
        additional_meta: Additional metadata

    Returns:
        Full CTM response dictionary including publickey, links, etc. for RSA keys
    """
    key_material_len=len(key_material)
    headers = {'Authorization': f'Bearer {api_key}'}

    # Start with basic metadata
    meta_data = {"ownerId": own}

    # Add additional metadata if provided
    if additional_meta:
        meta_data.update(additional_meta)

    # Build CTM key data with algorithm-specific parameters
    if algorithm.upper() == 'RSA':
        # For RSA keys, send PEM format directly (not hex-encoded)
        material_value = key_material.decode('utf-8') if isinstance(key_material, bytes) else key_material
        ctm_key_data = {
            "name": name_key,
            "usageMask": 12,
            "algorithm": algorithm,
            "meta": meta_data,
            "state": "Pre-Active",
            "unexportable": export,
            "material": material_value,
            "objectType": "Private Key",
            "aliases": [ { "alias": name_key, "type": "string" } ]
        }
    else:
        # For other algorithms, use hex encoding
        ctm_key_data = {
            "name": name_key,
            "usageMask": 12,
            "algorithm": algorithm,
            "meta": meta_data,
            "state": "Pre-Active",
            "unexportable": export,
            "material": key_material.hex(),
            "aliases": [ { "alias": name_key, "type": "string" } ]
        }

    # Use base_url directly if it's a complete URL, otherwise treat as IP for backward compatibility
    if base_url.startswith(('http://', 'https://')):
        api_url = f"{base_url.rstrip('/')}/api/v1/vault/keys2"
    else:
        # Backward compatibility: treat as IP address
        api_url = f"https://{base_url}/api/v1/vault/keys2"

    response = requests.post(api_url, headers=headers, json=ctm_key_data, verify=False)

    if response.status_code not in [200, 201]:
        print(f"Error uploading key to CTM: {response.status_code} {response.text}")
        raise Exception(f"CTM upload failed: {response.status_code} {response.text}")

    content = json.loads(response.content)
    ctm_key_id = content['id']

    print(f"Key {name_key} uploaded successfully, CTM Key ID is: {ctm_key_id}")

    # For RSA keys, log additional information if present
    if algorithm.upper() == 'RSA' and 'publickey' in content:
        print(f"RSA public key included in response")
        if 'links' in content:
            print(f"RSA key links: {len(content['links'])} link(s)")

    return content  # Return the full CTM response

# ---------Routine to get api key from CTM -----------
def ctm_get_api_key(base_url, login_data):
# Suppress the warnings from urllib3
    requests.packages.urllib3.disable_warnings(category=InsecureRequestWarning)
    
    # Use base_url directly if it's a complete URL, otherwise treat as IP for backward compatibility
    if base_url.startswith(('http://', 'https://')):
        api_url = f"{base_url.rstrip('/')}/api/v1/auth/tokens"
    else:
        # Backward compatibility: treat as IP address
        api_url = f"https://{base_url}/api/v1/auth/tokens"
    
    response = requests.post(api_url, json=login_data, verify=False)
    data = str(response.content).split('"')[1::2]
    #print(data)
    api_key = data[1]
    #print(api_key)
    if (api_key):
        print("Connection to CipherTrust Manager.......................success")
    else:
        print("Connection to CipherTrust Manager: Error\nExit program.")
        sys.exit(0) 
    return(api_key)
# ---------End get api key routine -----------

# ---------Routine to check if a key exists in CTM -----------
def ctm_key_exists(base_url, api_key, name_key):
    """
    Check if a key with the given name already exists in the CTM.
    Returns True if exists, False if not.
    Raises exception on error.
    """
    headers = {'Authorization': f'Bearer {api_key}'}
    params = {'name': name_key}
    
    # Use base_url directly if it's a complete URL, otherwise treat as IP for backward compatibility
    if base_url.startswith(('http://', 'https://')):
        api_url = f"{base_url.rstrip('/')}/api/v1/vault/keys2"
    else:
        # Backward compatibility: treat as IP address
        api_url = f"https://{base_url}/api/v1/vault/keys2"
    
    try:
        response = requests.get(
            api_url,
            headers=headers,
            params=params,
            verify=False
        )
        if response.status_code == 404:
            # No keys found with that name
            return False
        elif response.status_code != 200:
            raise Exception(f"Error querying CTM: {response.status_code} {response.text}")
        
        content = response.json()
        
        # Handle different response formats
        if isinstance(content, dict) and 'resources' in content:
            keys = content['resources']
        elif isinstance(content, list):
            keys = content
        else:
            # Single key object or other format
            keys = [content] if content else []
        
        # Ensure keys is a list and not None
        if keys is None:
            return False
        
        # Check if any key matches the name exactly
        for key in keys:
            if isinstance(key, dict) and 'name' in key and key['name'] == name_key:
                print(f"Key '{name_key}' already exists in CTM")
                return True
        
        return False
        
    except requests.exceptions.RequestException as e:
        print(f"Network error checking if key exists: {e}")
        raise
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON response: {e}")
        raise
    except Exception as e:
        print(f"Error checking if key exists: {e}")
        raise

