# Use Python 3.9 slim image as base for smaller image size
FROM python:3.9-slim

# Set environment variables for Python and Flask
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    FLASK_APP=run.py \
    FLASK_ENV=production \
    FLASK_HOST=0.0.0.0 \
    PORT=3001

# Set work directory
WORKDIR /app

# Install system dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        gcc \
        libc6-dev \
        curl \
        openssl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first to leverage Docker cache
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip \
    && pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Copy .env file if it exists (for environment variables)
# Note: .env files should be used for development only
COPY .env* ./

# Create non-root user for security best practices
RUN groupadd -r appuser && useradd -r -g appuser appuser \
    && chown -R appuser:appuser /app
USER appuser

# Expose port 3001 (SeQRNG-CTM API default port)
EXPOSE 3001

# Health check endpoint to monitor container health
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:${PORT:-3001}/api/v1/health || exit 1

# Run the SeQRNG-CTM API application
CMD ["python", "run.py"]
